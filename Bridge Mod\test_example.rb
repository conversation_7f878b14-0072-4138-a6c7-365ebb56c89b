# Bridge NPC Management System - Test Example
# This file demonstrates how to use the bridge NPC system

# Example 1: Auto-setup all bridge NPCs on map entry
# Place this in a map's autorun event or in the map's script
def setup_bridge_npcs_example
  # This will automatically find all events with "z100" or "z0" in their names
  # and set them up for bridge management
  count = pbAutoSetupAllBridgeNPCs
  if count > 0
    pbMessage("\\PN found #{count} bridge NPCs on this map!")
  end
end

# Example 2: Manual bridge control event
# Place this in an event that the player can interact with (like a switch)
def bridge_control_example
  if $PokemonGlobal.bridge > 0
    pbMessage("The bridge is currently raised.")
    if pbConfirmMessage("Lower the bridge?")
      pbMessage("The bridge mechanism creaks as it lowers...")
      pbBridgeOff  # This automatically updates all bridge NPCs
      pbMessage("The bridge is now lowered!")
      pbMessage("You can now interact with NPCs below the bridge.")
    end
  else
    pbMessage("The bridge is currently lowered.")
    if pbConfirmMessage("Raise the bridge?")
      pbMessage("The bridge mechanism whirs as it rises...")
      pbBridgeOn   # This automatically updates all bridge NPCs
      pbMessage("The bridge is now raised!")
      pbMessage("You can now interact with NPCs on the bridge.")
    end
  end
end

# Example 3: Conditional NPC interaction
# Place this in an NPC event's conditional branch to check if they should be interactable
def conditional_npc_example
  # For an NPC that should only be interactable when bridge is active (z100 NPC)
  if pbBridgeNPCInteractable?(get_self.id, true)
    pbMessage("Hello! I'm standing on the bridge!")
    pbMessage("I can only talk when the bridge is raised.")
  else
    # This branch shouldn't normally execute due to the interaction override,
    # but it's here for completeness
    pbMessage("...")  # NPC doesn't respond
  end
end

# Example 4: Advanced setup with custom z-levels
def advanced_setup_example
  # Set up an NPC with custom z-levels
  guard_id = 5  # Event ID of the guard
  pbSetupBridgeNPC(guard_id, 150, 25)  # Above: 150, Below: 25
  
  # Check if an NPC is bridge-managed
  if BridgeNPCManager.bridge_managed?(guard_id)
    pbMessage("Guard is now managed by the bridge system!")
  end
  
  # Get bridge data for an NPC
  data = BridgeNPCManager.get_bridge_data(guard_id)
  if data
    pbMessage("Guard's above z-level: #{data[:above_z]}")
    pbMessage("Guard's below z-level: #{data[:below_z]}")
  end
end

# Example 5: Event naming examples
# When creating events in the map editor, use these naming patterns:

# Bridge NPCs (above bridge when active):
# - "Guard z100"
# - "Merchant z100" 
# - "Bridge Keeper z100"
# - "Tourist z100"

# Below-bridge NPCs (below bridge when active):
# - "Fisher z0"
# - "Child z0"
# - "Boat Captain z0"
# - "Swimmer z0"

# Example 6: Complete bridge scene setup
def complete_bridge_scene_example
  # This would typically be called in a map's autorun event
  
  # Auto-setup all bridge NPCs
  pbAutoSetupAllBridgeNPCs
  
  # Set initial bridge state (optional)
  pbBridgeOff  # Start with bridge lowered
  
  pbMessage("Welcome to the bridge area!")
  pbMessage("Use the control panel to raise or lower the bridge.")
  pbMessage("Different NPCs will be available depending on the bridge state.")
end

# Example 7: Debugging and cleanup
def debug_bridge_system_example
  # Check current bridge state
  if $PokemonGlobal.bridge > 0
    pbMessage("Bridge is active (height: #{$PokemonGlobal.bridge})")
  else
    pbMessage("Bridge is inactive")
  end
  
  # List all bridge-managed NPCs on current map
  count = 0
  $game_map.events.each do |event_id, event|
    if BridgeNPCManager.bridge_managed?(event_id)
      count += 1
      data = BridgeNPCManager.get_bridge_data(event_id)
      pbMessage("Event #{event_id} (#{event.name}): Above=#{data[:above_z]}, Below=#{data[:below_z]}")
    end
  end
  pbMessage("Total bridge NPCs: #{count}")
  
  # Clear all bridge data (for debugging only)
  if pbConfirmMessage("Clear all bridge NPC data? (Debug only)")
    BridgeNPCManager.clear_all_data
    pbMessage("All bridge NPC data cleared!")
  end
end

# Example 8: Integration with other systems
def integration_example
  # Example of using bridge NPCs with other game mechanics
  
  # Check if a specific NPC is interactable before giving them items
  guard_id = 3
  if pbBridgeNPCInteractable?(guard_id, true)  # Guard is z100 NPC
    pbMessage("The guard is on duty and can accept your report.")
    # Give quest item, etc.
  else
    pbMessage("The guard is not at their post right now.")
  end
  
  # Use bridge state for environmental storytelling
  if $PokemonGlobal.bridge > 0
    pbMessage("The raised bridge blocks the river traffic below.")
    pbMessage("Boats wait patiently for the bridge to lower.")
  else
    pbMessage("Boats sail freely under the lowered bridge.")
    pbMessage("The bridge walkway is inaccessible from here.")
  end
end
