# Bridge NPC Management System

This plugin provides a comprehensive system for managing NPC sprite rendering priorities and interaction states based on bridge mechanics in Pokemon Essentials.

## Features

### Core Functionality
- **Name-based Z-level Detection**: NPCs with "z100" in their event name render above bridges, while NPCs with "z0" render below bridges
- **Bridge State-dependent Interaction**: NPCs can only be interacted with when they're on the "active" layer
- **Shadow Rendering**: NPC shadows automatically follow the same z-level as their corresponding NPC
- **Movement Mechanics**: NPCs have the THROUGH option enabled/disabled appropriately for proper walkability

### Automatic Setup
- **Auto-detection**: The system automatically detects and sets up bridge NPCs based on their names
- **Map Transfer**: Bridge NPCs are automatically configured when entering new maps
- **Integration**: Seamlessly integrates with existing `pbBridgeOn` and `pbBridgeOff` functions

## Usage

### Event Naming Convention

To use the automatic setup, name your events with z-level identifiers:

- **"Guard z100"** - NPC will be above bridge when bridge is active
- **"Fisher z0"** - NPC will be below bridge when bridge is active
- **"Merchant z100"** - Another above-bridge NPC
- **"Child z0"** - Another below-bridge NPC

The system extracts the number after "z" (case-insensitive) to determine the z-level.

### Bridge States

- **Bridge Active** (`pbBridgeOn`):
  - NPCs with z100 (above bridge) are visible and interactable
  - NPCs with z0 (below bridge) are hidden/non-interactable and have THROUGH enabled
  
- **Bridge Inactive** (`pbBridgeOff`):
  - NPCs with z0 (below bridge) are visible and interactable
  - NPCs with z100 (above bridge) are hidden/non-interactable and have THROUGH enabled

### Script Commands

#### Automatic Setup
```ruby
# Auto-setup single NPC based on name
pbAutoSetupBridgeNPC(event_id)

# Auto-setup all bridge NPCs on current map
pbAutoSetupAllBridgeNPCs

# Check if NPC should be interactable
if pbBridgeNPCInteractable?(event_id, true)  # true = when bridge active
  # NPC can be interacted with
end
```

#### Manual Setup
```ruby
# Manual setup with custom z-levels
pbSetupBridgeNPC(event_id, above_z = 100, below_z = 0)

# Remove bridge management
pbRemoveBridgeNPC(event_id)
```

#### Advanced Usage
```ruby
# Direct module access
BridgeNPCManager.setup_bridge_npc(event_id, 150, 50)
BridgeNPCManager.bridge_managed?(event_id)
BridgeNPCManager.get_bridge_data(event_id)
```

## Integration

### With Existing Bridge System
The plugin automatically hooks into the existing `pbBridgeOn` and `pbBridgeOff` functions, so no changes to existing scripts are needed.

### With Overworld Shadows Plugin
If the Overworld Shadows plugin is present, shadows will automatically follow their NPC's z-level changes.

### Event Interaction
The system overrides event interaction to ensure NPCs can only be interacted with when they're on the active layer.

## Examples

### Basic Bridge Scene
1. Create a bridge tile that can be activated/deactivated
2. Place NPCs with appropriate names:
   - "Guard z100" (stands on the bridge)
   - "Fisher z0" (stands under the bridge)
3. Use `pbBridgeOn` to activate the bridge
4. The guard becomes interactable, the fisher becomes non-interactable
5. Use `pbBridgeOff` to deactivate the bridge
6. The fisher becomes interactable, the guard becomes non-interactable

### Event Script Example
```ruby
# In an event that controls the bridge
if $PokemonGlobal.bridge > 0
  pbMessage("The bridge lowers...")
  pbBridgeOff
  pbMessage("You can now talk to people below the bridge.")
else
  pbMessage("The bridge raises...")
  pbBridgeOn
  pbMessage("You can now talk to people on the bridge.")
end
```

## Technical Details

### Z-Level Management
- Above bridge: Z-level 100 (or custom value)
- Below bridge: Z-level 0 (or custom value)
- Shadows: Always 1 level below their NPC

### Data Storage
- Bridge event data is stored in module class variables
- Original event properties are preserved for restoration
- Data persists across map changes but is automatically reconfigured

### Performance
- Minimal performance impact
- Only processes events that are explicitly marked as bridge-managed
- Automatic cleanup when events are removed from bridge management

## Troubleshooting

### NPCs Not Responding to Bridge Changes
- Ensure event names contain "z100" or "z0"
- Check that `pbAutoSetupAllBridgeNPCs` was called or events were manually set up
- Verify bridge state with `$PokemonGlobal.bridge`

### Shadow Issues
- Shadows automatically follow NPC z-levels if Overworld Shadows plugin is present
- No additional configuration needed

### Interaction Problems
- NPCs on inactive layers should not be interactable
- Use `pbBridgeNPCInteractable?` to check interaction state in conditional branches
