#===============================================================================
# Bridge NPC Management System
# Manages NPC sprites, priorities, and interactions based on bridge states
# Now supports automatic z-level detection from event names (e.g., "z100", "z0")
#===============================================================================

module BridgeNPCManager
  # Constants for z-level management
  BRIDGE_ABOVE_Z = 100    # Z-level for NPCs above bridges
  BRIDGE_BELOW_Z = 0      # Z-level for NPCs below bridges

  # Hash to store original event data for restoration
  @@original_event_data = {}

  # Hash to track bridge-managed events
  @@bridge_events = {}

  #=============================================================================
  # Name-based Z-Level Detection
  #=============================================================================

  # Extracts z-level from event name (e.g., "Guard z100" -> 100, "Fisher z0" -> 0)
  # Returns nil if no z-level found in name
  def self.extract_z_level_from_name(event_name)
    return nil unless event_name
    match = event_name.match(/z(\d+)/i)
    return match ? match[1].to_i : nil
  end

  # Automatically sets up bridge NPC based on name
  # If name contains "z100", NPC will be above bridge when active
  # If name contains "z0", NPC will be below bridge when active
  # Returns true if setup successful, false if no z-level found in name
  def self.auto_setup_bridge_npc(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    event = get_event(event_id, map_id)
    return false unless event

    z_level = extract_z_level_from_name(event.name)
    return false unless z_level

    if z_level >= BRIDGE_ABOVE_Z
      # High z-level means NPC is above bridge when active
      setup_bridge_npc(event_id, z_level, BRIDGE_BELOW_Z, map_id)
    else
      # Low z-level means NPC is below bridge when active
      setup_bridge_npc(event_id, BRIDGE_ABOVE_Z, z_level, map_id)
    end

    return true
  end

  # Scans all events on current map and auto-sets up bridge NPCs based on names
  def self.auto_setup_all_bridge_npcs(map_id = nil)
    map_id ||= $game_map.map_id
    map = $map_factory.getMap(map_id)
    return false unless map

    setup_count = 0
    map.events.each do |event_id, event|
      if auto_setup_bridge_npc(event_id, map_id)
        setup_count += 1
      end
    end

    return setup_count
  end

  #=============================================================================
  # Core Functions
  #=============================================================================

  # Sets up an NPC for bridge-based rendering and interaction
  # Parameters:
  #   event_id: The ID of the event to manage
  #   above_z: Z-level when bridge is active (default: 100)
  #   below_z: Z-level when bridge is inactive (default: 0)
  #   map_id: Map ID (defaults to current map)
  def self.setup_bridge_npc(event_id, above_z = BRIDGE_ABOVE_Z, below_z = BRIDGE_BELOW_Z, map_id = nil)
    map_id ||= $game_map.map_id
    event = get_event(event_id, map_id)
    return false unless event

    # Store original event data if not already stored
    unless @@original_event_data.has_key?([map_id, event_id])
      @@original_event_data[[map_id, event_id]] = {
        :through => event.through,
        :original_z => nil  # Will be set when first calculated
      }
    end
    
    # Register this event as bridge-managed
    @@bridge_events[[map_id, event_id]] = {
      :above_z => above_z,
      :below_z => below_z
    }
    
    # Apply current bridge state
    update_bridge_npc(event_id, map_id)
    return true
  end
  
  # Updates a single NPC based on current bridge state
  def self.update_bridge_npc(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    event = get_event(event_id, map_id)
    return false unless event
    
    bridge_data = @@bridge_events[[map_id, event_id]]
    return false unless bridge_data
    
    if $PokemonGlobal.bridge > 0
      # Bridge is active - NPC above bridge
      set_event_z_level(event, bridge_data[:above_z])
      set_event_through(event, true) if bridge_data[:below_z] == 0
    else
      # Bridge is inactive - NPC below bridge
      set_event_z_level(event, bridge_data[:below_z])
      set_event_through(event, true) if bridge_data[:above_z] > 0
    end
    
    return true
  end
  
  # Updates all bridge-managed NPCs
  def self.update_all_bridge_npcs
    @@bridge_events.each do |key, data|
      map_id, event_id = key
      update_bridge_npc(event_id, map_id)
    end
  end
  
  # Removes bridge management from an NPC and restores original settings
  def self.remove_bridge_npc(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    event = get_event(event_id, map_id)
    return false unless event
    
    # Restore original settings
    original_data = @@original_event_data[[map_id, event_id]]
    if original_data
      event.through = original_data[:through]
      # Reset z-level to default calculation
      reset_event_z_level(event)
    end
    
    # Remove from tracking
    @@bridge_events.delete([map_id, event_id])
    @@original_event_data.delete([map_id, event_id])
    
    return true
  end
  
  #=============================================================================
  # Helper Functions
  #=============================================================================
  
  # Gets an event by ID and map
  def self.get_event(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    if map_id == $game_map.map_id
      return $game_map.events[event_id]
    else
      map = $map_factory.getMapNoAdd(map_id)
      return map ? map.events[event_id] : nil
    end
  end
  
  # Sets the z-level for an event by modifying its screen_z calculation
  def self.set_event_z_level(event, z_level)
    # Store the custom z-level in the event
    event.instance_variable_set(:@custom_z_level, z_level)
    
    # Override the screen_z method for this specific event
    def event.screen_z(height = 0)
      custom_z = instance_variable_get(:@custom_z_level)
      return custom_z if custom_z
      
      # Fall back to original calculation
      return 999 if @always_on_top
      z = screen_y_ground
      if @tile_id > 0
        begin
          return z + (self.map.priorities[@tile_id] * 32)
        rescue
          raise "Event's graphic is an out-of-range tile (event #{@id}, map #{self.map.map_id})"
        end
      end
      return z + ((height > Game_Map::TILE_HEIGHT) ? Game_Map::TILE_HEIGHT - 1 : 0)
    end
  end
  
  # Resets an event's z-level to default calculation
  def self.reset_event_z_level(event)
    event.remove_instance_variable(:@custom_z_level) if event.instance_variable_defined?(:@custom_z_level)
    
    # Restore original screen_z method
    event.define_singleton_method(:screen_z) do |height = 0|
      return 999 if @always_on_top
      z = screen_y_ground
      if @tile_id > 0
        begin
          return z + (self.map.priorities[@tile_id] * 32)
        rescue
          raise "Event's graphic is an out-of-range tile (event #{@id}, map #{self.map.map_id})"
        end
      end
      return z + ((height > Game_Map::TILE_HEIGHT) ? Game_Map::TILE_HEIGHT - 1 : 0)
    end
  end
  
  # Sets the through property for an event
  def self.set_event_through(event, through_value)
    event.through = through_value
  end
  
  # Checks if an event is bridge-managed
  def self.bridge_managed?(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    return @@bridge_events.has_key?([map_id, event_id])
  end
  
  # Gets bridge data for an event
  def self.get_bridge_data(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    return @@bridge_events[[map_id, event_id]]
  end
  
  # Clears all bridge management data (useful for debugging)
  def self.clear_all_data
    @@bridge_events.clear
    @@original_event_data.clear
  end
end

#===============================================================================
# Integration with existing bridge functions
#===============================================================================

# Override pbBridgeOn to update bridge NPCs
alias bridge_npc_pbBridgeOn pbBridgeOn
def pbBridgeOn(height = 2)
  bridge_npc_pbBridgeOn(height)
  BridgeNPCManager.update_all_bridge_npcs
end

# Override pbBridgeOff to update bridge NPCs
alias bridge_npc_pbBridgeOff pbBridgeOff
def pbBridgeOff
  bridge_npc_pbBridgeOff
  BridgeNPCManager.update_all_bridge_npcs
end

#===============================================================================
# Shadow System Integration
#===============================================================================

# Extend the shadow system to respect custom z-levels
class Sprite_Character
  class ShadowSprite
    alias bridge_npc_position position
    
    private
    
    def position
      if @event.jumping?
        @sprite.y = @event.screen_y - 6 - jump_offset
        @sprite.zoom = 1 - (0.5 * jump_zoom)
      else
        @sprite.y = @event.screen_y - 6
        @sprite.zoom = 1
      end
      @sprite.x = @event.screen_x
      
      # Use the event's actual z-level (including custom z-levels)
      base_z = @event.screen_z
      @sprite.z = base_z - 1
    end
  end
end

#===============================================================================
# Convenience Functions for Event Commands
#===============================================================================

# Quick setup function for common bridge NPC scenarios
def pbSetupBridgeNPC(event_id, above_z = 100, below_z = 0)
  BridgeNPCManager.setup_bridge_npc(event_id, above_z, below_z)
end

# Auto-setup bridge NPC based on name (e.g., "Guard z100" or "Fisher z0")
def pbAutoSetupBridgeNPC(event_id)
  BridgeNPCManager.auto_setup_bridge_npc(event_id)
end

# Auto-setup all bridge NPCs on current map based on their names
def pbAutoSetupAllBridgeNPCs
  count = BridgeNPCManager.auto_setup_all_bridge_npcs
  pbMessage("Set up #{count} bridge NPCs automatically.") if count > 0
  return count
end

# Remove bridge management from an NPC
def pbRemoveBridgeNPC(event_id)
  BridgeNPCManager.remove_bridge_npc(event_id)
end

# Check if bridge is active and NPC should be interactable
def pbBridgeNPCInteractable?(event_id, when_bridge_active = true)
  bridge_data = BridgeNPCManager.get_bridge_data(event_id)
  return true unless bridge_data  # Not bridge-managed, always interactable

  bridge_active = $PokemonGlobal.bridge > 0

  if when_bridge_active
    # NPC is interactable when bridge is active (NPC is above bridge)
    return bridge_active
  else
    # NPC is interactable when bridge is inactive (NPC is below bridge)
    return !bridge_active
  end
end
