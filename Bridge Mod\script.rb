#===============================================================================
# Bridge NPC Management System
# Manages NPC sprites, priorities, and interactions based on bridge states
# Supports automatic z-level detection from event names (e.g., "z100", "z0")
#===============================================================================

module BridgeNPCManager
  # Constants for z-levels
  BRIDGE_ABOVE_Z = 100  # Z-level for NPCs above bridge
  BRIDGE_BELOW_Z = 0    # Z-level for NPCs below bridge

  # Storage for bridge-managed events
  @@bridge_events = {}
  @@original_event_data = {}

  # Helper method to get an event by ID and map
  def self.get_event(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    return nil unless $data_map_events && $data_map_events[map_id]
    return $game_map.events[event_id] if map_id == $game_map.map_id
    # For events on other maps, we'd need to load them differently
    # For now, only support current map
    return nil
  end

  # Extracts z-level from event name (e.g., "Guard z100" -> 100, "Fisher z0" -> 0)
  def self.extract_z_level_from_name(name)
    return nil unless name
    match = name.match(/z(\d+)/i)
    return match ? match[1].to_i : nil
  end

  # Automatically sets up bridge NPC based on name
  # If name contains "z100", NPC will be above bridge when active
  # If name contains "z0", NPC will be below bridge when active
  # Returns true if setup successful, false if no z-level found in name
  def self.auto_setup_bridge_npc(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    event = get_event(event_id, map_id)
    return false unless event

    z_level = extract_z_level_from_name(event.name)
    return false unless z_level

    if z_level >= BRIDGE_ABOVE_Z
      # High z-level means NPC is above bridge when active
      setup_bridge_npc(event_id, z_level, BRIDGE_BELOW_Z, map_id)
    else
      # Low z-level means NPC is below bridge when active
      setup_bridge_npc(event_id, BRIDGE_ABOVE_Z, z_level, map_id)
    end

    return true
  end

  # Auto-setup all bridge NPCs on current map based on their names
  def self.auto_setup_all_bridge_npcs(map_id = nil)
    map_id ||= $game_map.map_id
    return 0 unless $game_map.events

    count = 0
    $game_map.events.each do |event_id, event|
      next unless event && event.name
      if extract_z_level_from_name(event.name)
        if auto_setup_bridge_npc(event_id, map_id)
          count += 1
        end
      end
    end
    return count
  end

  # Sets up an NPC for bridge-based rendering and interaction
  # Parameters:
  #   event_id: The ID of the event to manage
  #   above_z: Z-level when bridge is active (default: 100)
  #   below_z: Z-level when bridge is inactive (default: 0)
  #   map_id: Map ID (defaults to current map)
  def self.setup_bridge_npc(event_id, above_z = BRIDGE_ABOVE_Z, below_z = BRIDGE_BELOW_Z, map_id = nil)
    map_id ||= $game_map.map_id
    event = get_event(event_id, map_id)
    return false unless event

    # Store original event data if not already stored
    unless @@original_event_data.has_key?([map_id, event_id])
      @@original_event_data[[map_id, event_id]] = {
        :through => event.through,
        :original_z => nil  # Will be set when first calculated
      }
    end

    # Register this event as bridge-managed
    @@bridge_events[[map_id, event_id]] = {
      :above_z => above_z,
      :below_z => below_z
    }

    # Apply current bridge state
    update_bridge_npc(event_id, map_id)
    return true
  end

  # Updates a single NPC based on current bridge state
  def self.update_bridge_npc(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    event = get_event(event_id, map_id)
    return false unless event

    bridge_data = @@bridge_events[[map_id, event_id]]
    return false unless bridge_data

    if $PokemonGlobal.bridge > 0
      # Bridge is active - NPC above bridge
      set_event_z_level(event, bridge_data[:above_z])
      set_event_through(event, true) if bridge_data[:below_z] == 0
    else
      # Bridge is inactive - NPC below bridge
      set_event_z_level(event, bridge_data[:below_z])
      set_event_through(event, true) if bridge_data[:above_z] > 0
    end

    return true
  end

  # Updates all bridge-managed NPCs
  def self.update_all_bridge_npcs
    @@bridge_events.each do |key, data|
      map_id, event_id = key
      update_bridge_npc(event_id, map_id)
    end
  end

  # Removes bridge management from an NPC and restores original settings
  def self.remove_bridge_npc(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    event = get_event(event_id, map_id)
    return false unless event

    # Restore original settings
    original_data = @@original_event_data[[map_id, event_id]]
    if original_data
      event.through = original_data[:through]
      # Reset z-level to default calculation
      reset_event_z_level(event)
    end

    # Remove from tracking
    @@bridge_events.delete([map_id, event_id])
    @@original_event_data.delete([map_id, event_id])

    return true
  end

  private

  # Sets the z-level for an event by modifying its screen_z calculation
  def self.set_event_z_level(event, z_level)
    # Store the custom z-level in the event
    event.instance_variable_set(:@custom_z_level, z_level)

    # Override the screen_z method for this specific event
    def event.screen_z(height = 0)
      custom_z = instance_variable_get(:@custom_z_level)
      return custom_z if custom_z

      # Fall back to original calculation
      return 999 if @always_on_top
      z = screen_y_ground
      if @tile_id > 0
        begin
          return z + (self.map.priorities[@tile_id] * 32)
        rescue
          raise "Event's graphic is an out-of-range tile (event #{@id}, map #{self.map.map_id})"
        end
      end
      return z + ((height > Game_Map::TILE_HEIGHT) ? Game_Map::TILE_HEIGHT - 1 : 0)
    end
  end

  # Resets an event's z-level to default calculation
  def self.reset_event_z_level(event)
    event.remove_instance_variable(:@custom_z_level) if event.instance_variable_defined?(:@custom_z_level)

    # Restore original screen_z method
    event.define_singleton_method(:screen_z) do |height = 0|
      return 999 if @always_on_top
      z = screen_y_ground
      if @tile_id > 0
        begin
          return z + (self.map.priorities[@tile_id] * 32)
        rescue
          raise "Event's graphic is an out-of-range tile (event #{@id}, map #{self.map.map_id})"
        end
      end
      return z + ((height > Game_Map::TILE_HEIGHT) ? Game_Map::TILE_HEIGHT - 1 : 0)
    end
  end

  # Sets the through property for an event
  def self.set_event_through(event, through_value)
    event.through = through_value
  end

  # Checks if an event is bridge-managed
  def self.bridge_managed?(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    return @@bridge_events.has_key?([map_id, event_id])
  end

  # Gets bridge data for an event
  def self.get_bridge_data(event_id, map_id = nil)
    map_id ||= $game_map.map_id
    return @@bridge_events[[map_id, event_id]]
  end

  # Clears all bridge management data (useful for debugging)
  def self.clear_all_data
    @@bridge_events.clear
    @@original_event_data.clear
  end
end

#===============================================================================
# Integration with existing bridge functions
#===============================================================================

# Override pbBridgeOn to update bridge NPCs
alias bridge_npc_pbBridgeOn pbBridgeOn
def pbBridgeOn(height = 2)
  bridge_npc_pbBridgeOn(height)
  BridgeNPCManager.update_all_bridge_npcs
end

# Override pbBridgeOff to update bridge NPCs
alias bridge_npc_pbBridgeOff pbBridgeOff
def pbBridgeOff
  bridge_npc_pbBridgeOff
  BridgeNPCManager.update_all_bridge_npcs
end

#===============================================================================
# Integration with Overworld Shadows plugin
#===============================================================================

# Ensure shadows follow the same z-level as their NPCs
if defined?(OverworldShadows)
  module OverworldShadows
    class Shadow
      alias bridge_npc_position position

      def position
        bridge_npc_position

        # Check if this event is bridge-managed and adjust shadow z accordingly
        if BridgeNPCManager.bridge_managed?(@event.id)
          # Get the event's current z-level (including custom z-level)
          event_z = @character.z
          @sprite.z = event_z - 1
        end
      end
    end
  end
end

#===============================================================================
# Convenience Functions for Event Commands
#===============================================================================

# Quick setup function for common bridge NPC scenarios
def pbSetupBridgeNPC(event_id, above_z = 100, below_z = 0)
  BridgeNPCManager.setup_bridge_npc(event_id, above_z, below_z)
end

# Auto-setup bridge NPC based on name (e.g., "Guard z100" or "Fisher z0")
def pbAutoSetupBridgeNPC(event_id)
  BridgeNPCManager.auto_setup_bridge_npc(event_id)
end

# Auto-setup all bridge NPCs on current map based on their names
def pbAutoSetupAllBridgeNPCs
  count = BridgeNPCManager.auto_setup_all_bridge_npcs
  pbMessage("Set up #{count} bridge NPCs automatically.") if count > 0
  return count
end

# Remove bridge management from an NPC
def pbRemoveBridgeNPC(event_id)
  BridgeNPCManager.remove_bridge_npc(event_id)
end

# Check if bridge is active and NPC should be interactable
def pbBridgeNPCInteractable?(event_id, when_bridge_active = true)
  bridge_data = BridgeNPCManager.get_bridge_data(event_id)
  return true unless bridge_data  # Not bridge-managed, always interactable

  bridge_active = $PokemonGlobal.bridge > 0

  if when_bridge_active
    # NPC is interactable when bridge is active (NPC is above bridge)
    return bridge_active
  else
    # NPC is interactable when bridge is inactive (NPC is below bridge)
    return !bridge_active
  end
end

#===============================================================================
# Event Interaction Override for Bridge NPCs
#===============================================================================

# Override event interaction to respect bridge states
class Game_Event
  alias bridge_npc_check_event_trigger_touch check_event_trigger_touch

  def check_event_trigger_touch(x, y)
    # Check if this event is bridge-managed
    if BridgeNPCManager.bridge_managed?(@event.id)
      bridge_data = BridgeNPCManager.get_bridge_data(@event.id)
      if bridge_data
        bridge_active = $PokemonGlobal.bridge > 0

        # Determine if NPC should be interactable based on bridge state
        if bridge_active
          # Bridge is active - only NPCs with high z-level (above bridge) are interactable
          return unless bridge_data[:above_z] > bridge_data[:below_z]
        else
          # Bridge is inactive - only NPCs with low z-level (below bridge) are interactable
          return unless bridge_data[:below_z] <= bridge_data[:above_z]
        end
      end
    end

    bridge_npc_check_event_trigger_touch(x, y)
  end
end

#===============================================================================
# Auto-setup on map transfer
#===============================================================================

# Automatically setup bridge NPCs when entering a new map
EventHandlers.add(:on_enter_map, :bridge_npc_auto_setup,
  proc { |old_map_id|
    # Auto-setup all bridge NPCs on the new map
    BridgeNPCManager.auto_setup_all_bridge_npcs
  }
)